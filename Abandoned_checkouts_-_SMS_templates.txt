






abandoned checkouts | SMS templates



SMS marketing is super powerful. Messaging on your phone is probably the most common way to socialize in 2022, let's be honest. That's why you have to leverage SMS marketing for your online store - because you can connect with your customers, quickly.

We're going to focus on abandoned checkouts in this PDF. Which app do I recommend? None really. I've been using SMS Bump, but you can use any app. If you do use SMS Bump (which I've been using for years), remember you need to go through their annoying GDPR requirements to get your account activated. But we have a solution for that. What I do is I copy my theme, as back-up. Then I make all their required changes to the published theme & to my checkout settings. I message them to activate the account, and once they do it - I switch to the back-up theme and change all my checkout settings back. That should work for all of you too. Make sure to turn o? quiet hours in the settings.

We're going to use ?ows, not automations. Here's the setup (including delays & conditions):




























































































































































Make sure to adjust all the templates to your store & product. Most of the {Functions} I wrote in each message should actually translate dynamically on your store (if you use SMS Bump). That means you can pretty much copy paste everything I gave you. Send yourself some test messages anyway.





If you have any questions, write them in our discord server.



written by  @eduardbeschea | mavenport



Important!

This document & the information on it are not to be shared by anyone other than <PERSON> & his admins. Giving away any of our assets (such as this document) will result in a permanent ban from the discord community.
Thank you for understanding.
