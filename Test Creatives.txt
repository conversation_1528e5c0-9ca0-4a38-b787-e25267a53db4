


testing new ad creatives | facebook ads


Testing new ad creatives is the most effective way to maintain a consistent performance with Facebook ads, and doing it the right way opens new opportunities to scale your profitability further.

In this PDF, I'll show you exactly how to test & implement new ad creatives (videos or images) into your current set up, and also how to potentially scale them.

Note: This strategy is meant for people who are testing new ad creatives for their already-running ad accounts. If you want to learn how to test your products with Facebook ads, follow the PDF & video dedicated to testing products from the #knowledge channel.



sourcing content

You can get content for your ads from multiple places, including ripping it straight from TikTok & YouTube, however, if you're at a point where you're serious about taking your business to the next level, you should hire actors or influencers to film videos for you.

The best places to find content creators are Upwork & Fiverr, but also TikTok & Instagram (for younger creators who may accept lower pay).

For example, if you need young women in your ads, you can search for your niche on TikTok, find the content creators that fit your brand, and click on their Instagram icon from their TikTok profile. Then, on Instagram, you can direct message them, or click on the mail icon on their profile & e-mail them, letting them know you're interested in working together.

Note: Stealing ads from other people is generally going to result in bad performance due to the Facebook algorithm showing your ads to the same people that have already been sold to by the main advertiser that you stole from, which leads to lack of interest, bad metrics, and potential reports from the owner.

It's also a massive beta move. If you want to make money with eCommerce, either hire your own actors or influencers, or at least edit your own ads using ripped content from TikTok or YouTube (if you're just starting out).



frequency & volume

I recommend that you test new creatives in a batch of new 4-6 ads, using the strategy you'll learn in this PDF. I found excessive testing leads to bad performance.

Also, I suggest testing a new batch of ads (4-6 creatives) once a month for stores selling under
$5,000/day, and once every 1-2 weeks for stores selling $10,000/day & more.



composition (angles & demographics)

In this section, I'll explain how to differentiate the 4-6 ads inside one batch of creatives you test (check the PDF on the best types of video ads to learn which type you should focus on), and how to help your ads be more efficient by leveraging different marketing angles.

Let's elaborate on the main 2 elements inside our ads:


Hooks: The first 2-5 seconds of your ad, dedicated to hooking your viewers in to watching the rest of the video, but - most importantly - helping Facebook target new audiences based on certain marketing angles.

Example hooks (if we sell a dress):

1. Targeting women about to attend a wedding (use female actors, age 20-30): "Here's the most gorgeous dress to wear for your bestie's special day!"

2. Targeting women that are insecure about their body (use female actors, aged 18-65+): "This dress slimmed my waist down 3 sizes!"

3. Targeting women struggling with discomfort (use female actors, aged 40+):
"I finally found a supportive, comfy dress I can wear ANYWHERE. Check this out!"

Body: The rest of the video ad, right after the hook, dedicated to convincing the customer to buy your product. You'll edit this content in a way to depict your product's benefits, cleverly placed inside a storyline.

Pro tip: Use a post-purchase survey app to ask customers more specific questions about the reason they bought your products, and then use those angles in your new ads. This is the app I use for it: https://www.stilyoapps.com/reconvert/v1/?scid=Kswx&ref=ST01-mavenport


If you're testing short & middle-form ads, I recommend using 2-3 different video bodies inside the 4-6 ad batch (so you can repeat them 1-3 times), and - of course - different hooks.

For long-form ads, I suggest following just 1storyline for an entire batch of creatives, inside just 1 video body. That means you'll follow 1 script, but 4-6 different hooks. The purpose of the hooks is to attack different pain points & marketing angles, which will encourage Facebook to find new profitable audiences for you (as explained in the hooks section above).



ad testing strategy

In this section, I'll teach you what I found to be the most efficient way to test new ad creatives on Facebook!

Note: I recommend excluding buyers from all new ad sets you create. You can create an audience of all buyers in the last 180 days using your pixel, and you can also export all your buyers from Shopify and upload them as a custom audience (do this 1-2 times per month, and add all new list to your new ad set exclusions). I exclude both of these audiences from all new ad sets I create. For ASCs, you just need to click on Edit in account settings at the campaign level, and add all audiences you want to exclude (only buyers), then, back in the campaign setup, set the Existing customer budget cap to 0%. Learn more about exclusions from our retargeting PDF.


Step 1 ($20/day per ad set)

1. Duplicate your best-performing ad set once (inside the same ABO campaign), and delete all your old ads from inside it. Set the scheduled starting date for midnight (ad account time), but don't publish it yet.

2. Create 4-6 new ads inside the new ad set you just duplicated. Completely finish the structure of all ads, besides adding your videos in them, so use the same ad copy, headlines, & link descriptions as in your old ad sets (or improve them if you'd like, but it's not super important). Add the website link and call-to-action button (usually "Shop now" is the best).

3. Now that all the new ads inside your ad set are exactly the same structure, go ahead and add a different new creative in each one of them (while also manually selecting the most catchy frame as a thumbnail for each, if they're videos).

4. Publish them, wait 2 minutes, refresh the page. Now turn the new 4-6 ads into existing posts (post IDs, as explained in the video I posted in the #knowledge channel).

5. Now that one ad set is completely finished, duplicate it 4-5 times - which means you'll end up with 5-6 new ad sets in total, all of them using the same new ads. Target your best performing country/ countries. If you have multiple country combinations (like USA alone, and then Top 4 countries together), then have 3-4 ad sets target the best combination, and the rest target the second best.

6. We'll change the interest/lookalike targeting now for the 4-5 new ad sets (the first new ad set you made is already targeting one of your best-performing audiences). Target different current best- performing interests in 1-2 of the remaining ad sets, and completely new interests in the rest. You should leave 1 ad set completely broad (no targeting).

7. Publish all new ad sets.


This is what your current ABO campaign should look like if you were to test 5 new creatives, and you found a combination of 12 profitable countries:










Step 2 ($200/day)

1. Create a new Advantage+ shopping campaign, add existing customers (only buyers), and set the Existing Customer Budget Cap to 0% (meaning that Facebook will avoid targeting people who already bought from you). Set the scheduled starting date for midnight (ad account time), but don't publish it yet. Target your best country/country combination.

2. Delete any ads that already exist inside this new campaign.

3. Go into one of the new ad sets you just created from Step 1, select the 4-6 ads you just made, and duplicate them into the single ad set inside your new ASC (Advantage+ shopping campaign). That way, they'll retain their post IDs.

4. Publish the campaign.


This is what your new ASC campaign should look like if you were to test 5 new creatives, and you found a combination of 12 countries that perform better than USA on its own:





when to kill new ad sets

Don't expect to be very profitable on the first day of testing new creatives. Remember, the whole purpose is to find 1-2 new high-performing ads, and only after that, optimize for profitability.

We want to spend a few thousand dollars (ideally) on the new ads in total, before we take a decision on which of them we'll move forward with.

However, if your new ad sets got some sales, once the first day is complete, we'll pause all the new ad sets that got 0 purchases inside the ABO.

If your new ad sets got no sales at all, but you're profitable overall (at the ad account level), leave the ones that got 1-2 carts/checkouts to run another day, because the good momentum we have can afford extending the test. We'll even duplicate 2-3 of them & schedule them for the coming midnight, targeting different interests.

Note: Do not pause ads in this process, only ad sets.

Even if the new ad sets aren't doing that well initially, we'll still duplicate the best performing ones 2-6 times (the more profitable you are, the more duplicates you make) for the second day of the test, to give them another chance to pick up.

For the ASC, only pause it if it got 0 sales on the first day, otherwise let it run another day, even if it's very unprofitable. If it's unprofitable for 2-3 days in a row, kill it.



scaling new ads

The ad sets & campaign that we created in order to test our new creatives can actually be scaled!

Scaling the new ad sets
If your new ad sets containing just the new creatives are profitable, we'll continue duplicating them and trying new interests (and even old ones that used to perform well), finding new profitable ad + audience combinations.

Once a new ad set has spent over $60 at around 30% profit margin, duplicate it into a CBO alone at
$100-200/day, leaving just the new ads inside, right as it is.


Scaling the new Advantage+ shopping campaign
If your ASC is bringing about 25-30% profit, scale it up by $100-200 during the day (after getting at least 3 sales). Keep raising the budget every day unless your profitability drops under 20%.

If your ASC turns unprofitable after scaling it for 2 days in a row, reduce the budget by around $200 at the end of the day. Keep reducing it until you're profitable again, and if you're not, kill it completely.



implementing the new winning ads into the mix

If your new ads have been doing well after spending a total of $2,000-$4,000, you'll want to implement the best ones into new ad sets (and new/old campaigns). Let me explain.

Usually, you'll find that 1-2 new ads completely outperform the rest of the ones in the test, so take note of that.

Also check the performance of all your old ads in the last 1-2 weeks, and take note of the ones that are profitable.

Now create new ad sets (by duplicating current best performing interests, as we did before), and only use the best 1-2 new ads you discovered, combined with the old profitable ads you just took note of
- all together in each new ad set. Target current best audiences, and new audiences, just like a normal test.

Note: Add "- 9best", for example, to the name of the new ad sets that are running your best combination of ads, so they're easier to track.

Continue treating them just like regular ad sets, scale them like regular ad sets, and pause them like regular ad sets - based on profitability.

You may also add the 1-2 new best ads you discovered into old CBOs, but never in ABOs or ASCs.



repeat

That's it, that's the strategy that currently works best for me for testing new creatives, with a scaling spin.

Note: Dynamic creative optimization hasn't worked as well as this for me, so I don't recommend it, but it's up to you if you'd like to test it.

Ideally, once you get really good at creating & testing new ads, hire editors & other employees to help you with it, in order to systemize the whole process, and make implementing new creatives seamless.



If you have any questions, write them in Mavenport discord.




written by  @eduardbeschea | mavenport



Important!

This document & the information in it are not to be shared by anyone other than Eddie & his admins. Giving away any of our assets (such as this PDF) will result in a permanent ban from the discord community.
Thank you for understanding.
