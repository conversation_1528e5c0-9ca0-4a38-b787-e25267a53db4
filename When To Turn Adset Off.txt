








when to turn ad sets o? | facebook ads




We all got some unpro?table ad sets, but could they turn around and become pro?table? When do we turn them o?? That's exactly what I'll teach you in this PDF.

These decisions are all taken based on the metrics of your ad sets. That's become a little di?cult lately due to Facebook's tracking issues, so you'll have to either rely on a tracking app or on UTMs in order to get a clearer view of your metrics. I use medium & campaign UTM parameters, and I explained in my scaling YouTube video how you set them up. It's not 100% accurate but I'm still able to scale to 5 ?gures per day with them. Once you learn your ideal KPIs, it gets easier to see which audiences are pro?table or not.

Alright, here we go!




1. for ads that you just started

Let's say that today (on day 0) you scheduled your ads for tomorrow, which will be your day 1. We'll take most of our decisions at the end of the day, but if your ad sets spent a total of about $30 in the middle of day 1 with no sales & a $2-3+ cost per click, get rid of them. If most of your ad sets are in that situation, turn everything o?. Most likely your ad creatives suck. Learn how to make better ads by reading the video ad creative structure pdf.

If your ad sets have good on platform metrics (CTR, CPC, good engagement, etc.) but they get no adds to cart or checkouts in about $30-40 USD, then, most likely, the problem is your website. It either has bad copywriting, no good, convincing angles, no good reviews (which you can add), no good o?er; or simply - your product isn't good enough. Either readjust the elements I just mentioned, or move on to another product. No need to waste time.

Disclaimer: Don't be impatient when you turn ad sets o?, wait until you have some data to judge; but also don't let them spend uselessly when they're clearly not working (like in the examples above).

If your ad sets are not in any of the situations from above, then you'll judge them again at the end of day 1. Let's say they end up looking like this (green = the ad set got at least a sale, red = the ad set got 0 sales, yellow = the ad set got a maximum of $10 cost per checkout):








In this case, you'll turn o? ad sets 4, 5, and 8, and you'll let ad sets 1, 2, 3, 6, and 7 run. If any of the yellow ad sets get no sales at the end of day 2, you turn them o?.







2. for ads that have been running for more than 3 days

At this point, you should be having multiple running ad sets. For all new ones that you create, we'll still apply the strategy from 1. (because they'll be on their day 1).

However, for ad sets that have been running for more than 3 days, we'll judge them based on a 3-day window (or 4-day window if you're spending less than $20/day per ad set). So let's say this is what they look like (green = the ad set is pro?table, red = the ad set is unpro?table, yellow = the ad set is breaking even / or very close).








In this case, you'll turn o? ad sets 4 and 8, and you let ad sets 1, 2, 3, 5, 6, and 7 run. If any yellow ad sets do not become pro?table on day 4, you turn them o?. Repeat this cycle every day.







The part 2 of this pdf (when to turn o? scaling CBO's) is coming soon.







If you have any questions, write them in our discord server.



written by  @eduardbeschea | mavenport



Important!

This document & the information on it are not to be shared by anyone other than Eddie & his admins. Giving away any of our assets (such as this document) will result in a permanent ban from the discord community.
Thank you for understanding.
