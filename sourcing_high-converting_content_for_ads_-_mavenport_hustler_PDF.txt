



sourcing high-converting content



Content is the lifeblood of an eCommerce store, offering it fuel to scale through paid ads or organic posts. In this PDF, I will teach you the best ways to source content, and also how to instruct the creators that you work with to receive the most effective videos & pictures for social media. This will not only improve the quality & results of your ads, but it'll make it close to effortless for you to get the content you need. Here's the entire recipe:





where to get it

While you can hunt for the right content creators for your brand on Instagram, TikTok, and YouTube, the most efficient way to source content is through hiring platforms:



Fiverr


Search for keywords such as spokesperson, U<PERSON>, (female or male) actor, and you'll get hundreds of creators come up. Look at all of them through multiple pages, and open each one that fits your target market in a separate tab. Message them all a copy-paste message like this (make sure to replace the
<placeholders> with your own data):



Once you get enough replies, pick as many content creators as you wish to work with, and ship your product to them - either from China with express shipping, or straight from Amazon, if it's on there.



Upwork


Here you'll post 2-3 jobs. Why not just 1? Because you want to test multiple titles ( just like you test multiple hooks when you run ads), to give yourself a better chance of finding the right creators. You don't need to set a price for all those jobs, but you can set just one of them to $100-200.

Your titles should describe the demographic you're looking for. Let's say we need someone for a posture corrector ad, these are some examples of efficient titles:

Looking for male UGC, age 35+ Need male/female spokesperson Need 40+ y/o female UGC
Looking for product demo video creator


Here's an example job description you can use (make sure to replace the <placeholders> with your own data):



Note 1: If you're looking for long-form content, make sure to have a clear script ready to give to the content creator you hire. If you need short or medium-form content, have a list of the raw clips you need ready.

Note 2: For short & middle-form content, I recommend always hiring at least 3 people for the same list of video clips & pictures because some won't be as good as others, and it's always great to get multiple perspectives/angles of your videos.




content structure

I'll show you some quick & simple examples of what your script / requested videos should look like.




5-25 seconds / 20-60 seconds
short-form / medium-form

For this type of content, we need 9:16 raw videos, which we'll later on edit as 4:5 or 9:16 (or rarely, 1:1). We'll offer content creators a simple list of raw videos (and pictures, if you'd like) that they need to get for us. Let's take a mini-skirt as the product example. I'll write a list of videos & pictures that we'd need in order to create ads and organic posts:




Note: The price of short-form content depends on multiple factors (the experience & talent of the creator, the length & difficulty of the content, number of people required, etc.), so it's hard for me to give you a clear range. For example, a 1-person script structured as in the example above could range between $100-300.



1-30 minutes
long-form (presentation)

For this type of content, we need either 9:16 or 16:9 raw videos, which we'll later on edit into 4:5 or 1:1 ads. I can't write a whole example script for you because it takes a lot of research & effort, and this PDF is not about script writing, but I'll show you what it should look like. Present this as a Google doc to your content creator (make sure to replace the <placeholders> with your own data):




Note: The price of long-form content depends on multiple factors (the experience & talent of the creator, the length & difficulty of the script, number of people required, etc.), so it's hard for me to give you a clear range. For example, a 1-person script structured as in the example above could range between $250-600.





content guidelines

Use the guidelines below to educate your content creator on how to film, behave, & position themselves in the frame. I'll write it in a format that allows you to simply copy & paste it directly into the script you'll give your creator. Let's go!




Now I'll talk about the ideal positioning for different aspect ratios. You can simply screenshot this & show it to your creator (based on which aspect ratio you need):


positioning for 9:16 videos (tall, like TikTok)

TikTok & Facebook place their interface & text at the top and bottom, which means that those parts of our videos will be covered & unusable.

Because of that, whenever we film in a 9:16 format, we'll always position the subject of the video (you + the product) inside a 4:5 frame.

So let's say we're selling a pink skirt.


I'll show you some examples of bad positioning, and an example of perfect positioning:


1.











Too far down.


2.	3.	4.

  
Too far up.	Better, but a bit too close.	Perfect!





positioning for 16:9 videos (wide, like YouTube)

Most people pay attention to the middle of wide videos, and the left & right sides are generally used for text pop-ups or platform interface.

Because of that, whenever we film in a 16:9 format, we'll always position the subject of the video (you + the product) inside a 1:1 (square) frame.

So let's say we're selling a pink skirt.


I'll show you some examples of bad positioning, and an example of perfect positioning:

1.	2.
Too far down.	Too far to the side.

3.	4.
Better, but a bit too close.	Perfect!



conclusion

If you informed the creators you picked just like I showed you above, the content you get should be close to perfect for editing (with exceptions, of course, because some of them are straight airheads).

Now either you or your editor will take all the raw videos and turn it into ads, organic posts, or website content, which will inevitably give you significantly more room to scale your store/s, and make more profit.






If you have any questions, write them in the Mavenport discord server.





written by  @eduardbeschea | mavenport



Important!


This document & the information in it are not to be shared by anyone other than Eddie. Giving away any of our assets (such as this PDF) will result in a permanent ban from the discord community.
Thank you for understanding.
