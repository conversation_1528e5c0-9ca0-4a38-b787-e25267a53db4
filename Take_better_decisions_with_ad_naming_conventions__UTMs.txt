




naming conventions & UTMs | facebook ads




It's important that you have clear naming conventions when you run Facebook ads, not only for you to have clarity inside the ads manager, but especially inside your Shopify analytics (when you check UTMs). This will help you take better decisions on what to cut o? and what to scale. Let's start!




how to name your ad sets and campaigns


Your initial ABO testing campaign

BM name - Ad account name | ABO Prospecting - Product name initials

Note: We'll include the business manager & ad account name at the beginning because you might end up using multiple of them, so you need to be able to di?erentiate them when you check your UTMs.



Example (testing a posture corrector):

BM4089 - USD32 | ABO Prospecting - PC





The ad sets inside your ABO testing campaign

BM name - Ad account name | ABO Prospecting - Product name initials | Interest name (targeted countries)



Example (for posture corrector ad sets):

BM4089 - USD32 | ABO Prospecting - PC | Health & Wellness (USA)





The ads inside any type of campaign

Product name initials - video/image variation number - ad copy number

Note: The variation number is simply based on how many videos or images you edited and published (so if you made 5 videos, number them from 1 to 5). The same goes for how many ad copies (text at the top of your ad) you're testing. Initially, just test 1.



2 examples (for posture corrector ads):

PC - video 1 - ad copy 1 PC - video 2 - ad copy 1





Scaling campaigns & the ad sets inside them

We'll name the campaign & the ad set the exact same, such as:

BM name - Ad account name | CBO number - Product name initials | Interest name (targeted countries)

Note: The CBO number is simply based on how many CBOs you've made. Start with "CBO 1" and go on from there.



2 examples (for posture corrector scaling campaigns):

BM4089 - USD32 | CBO 1 - PC | Health & Wellness (USA) BM4089 - USD32 | CBO 2 - PC | Healthy habits (USA)




adding your UTMs


Whenever you create your ?rst ad and turn it into a post ID, make sure to add UTMs to the URL parameter ?eld (ad level). When you duplicate your ads & ad sets, it'll be carried to each copy. Here's the code and what it should look like:





utm_source=Facebook&utm_medium={{ adset.name}} &utm_campaign={{campaign.name}} &utm_content={{ad.name}}
Add the code above into the ?eld you see in the image on the right.













quick examples







timing


Scaling
I use the UTM campaign name metrics throughout the day to ?nd out which of my CBOs are performing the best, and I increase the budget based on that (read the scaling PDF to learn how to scale).

Scheduling ad sets for the next day
4-7 hours before midnight (ad account time), I use the UTM campaign medium metrics and the ABO testing campaign name ?lter (as shown above) to see which interests performed well today (and also in the last 3 days). I then schedule new, related interests to start at midnight (ad account time).

Getting rid of bad performers
I use both of the metrics above 20-30 minutes before midnight (ad account time) to get rid of interests and campaigns that haven't been performing well (check the PDFs on when to turn ad sets & scaling CBOs o?).




As you can clearly see, UTMs aren't tracking every single metric either. We use UTMs as another
indicator to aid in our decision-making process.

Here's an example: You're very pro?table today and you're checking to see which ad sets or CBOs to scale. Facebook will track a few sales, but it's unreliable. UTMs will track a few more sales but they still indicate that a scaling CBO of yours was only breaking even after spending $300. Well, since you're pro?table that day, I'd be more inclined to say that your CBO is in fact pro?table, and scalable.

This will become a 6th sense after you gain some experience, but hopefully this helped you understand how to judge metrics a little better, and put you on the right path.




written by  @eduardbeschea | mavenport




Important!

This document & the information on it are not to be shared by anyone other than Eddie & his admins. Giving away any of our assets (such as this document) will result in a permanent ban from the discord community.
Thank you for understanding.
