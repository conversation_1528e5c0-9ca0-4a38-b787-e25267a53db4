







sourcing products | dropshipping



Once you pick a product that you'd like to sell, you'll probably be wondering: "Great! Now where can I actually ship it from? How do my customers get this product after placing orders on my store?".

No worries, I already got all this ?gured out for you. I've split this informative PDF into 2 sections:
1. ?nding a supplier when you have 0 sales (beginners) and 2. ?nding an agent once you have (some) consistent daily sales. So let's start!



1. ?nding a supplier when you have 0 sales (beginners)

This is how I started, and this is what I recommend that you do as well. After you found a potential winning product, you simply look it up on AliExpress and check the cost (product cost + shipping). If you can comfortably mark it up more than 3x, great!

Start testing your product and for the ?rst few orders, use AliExpress (and AliExpress Standard Shipping) to buy & ship it. Place each customer's order one by one and use their delivery address.

As an alternative, you can use apps like CJ Dropshipping to ship & ful?ll your orders. They're like a ful?llment center and can source products for you. I don't like the app at all, but just like AliExpress, you only have to do this at the beginning in order to see if your product does in fact sell.

I know you'll say that shipping takes too long from AliExpress, and it's true. But this is an easy compromise to accept. You'll do this just until you see that you can get 5-15 orders per day, and then you'll move to stage 2.

In the meantime, communicate politely about shipping times to your customers.

Remember, this is a short term move. You're only using AliExpress to validate your product, not to make money.






2. ?nding an agent once you have (some) consistent daily sales

Once you validate your product and you start getting 5-15 daily orders, it's time to look for a sourcing agent who can help you ship & ful?ll your orders. There are multiple ways to achieve that:

a) contact the current supplier from AliExpress and ask him if he'd be willing to collaborate with you outside of the platform. I, personally, don't like doing this.

b) ask the members of the communities you're part of (Facebook groups, Discord servers) for agent recommendations. This is a solid option because you'll receive referrals to agents that are currently helping others ship their orders e?ciently.

c) go on Alibaba.com and search for "dropshipping agent". In all honesty, this is how I found the best quality agents in the last few years. You can even start an RFQ (Request for Quotation) & explain what you're looking for.


Remember: the ?rst agent you talk to is most likely not going to be great. They'll probably not even answer you. Approach 20-30 with a copy-paste intro message. Pick the best 3-4 and chat with them further, compare prices and talk about their policies (returns, exchanges, refunds, schedule, etc.)






how to approach a sourcing agent?

Here's a copy-paste template you can send to all agents you get in touch with (you might want to change some data in order to match you better). Always exaggerate a little bit if you want people to get back to you. They want long-term business, remember, not a random newbie. Here it is:

Hey, my name is <Name>.

I've been dropshipping for <number> years and I own multiple stores, all selling a total of <revenue>
per month.

I'm looking for a new sourcing agent to ship & ful?ll all my orders on one of my new Shopify stores, I just started it and it's getting <number-number> of orders per day; I really think we can scale it together.

My customers mainly come from <countries>. Here's my main product: <AliExpress product link or image>

Ideally, I'm looking to dispatch all incoming orders within 24 hours (once we start building stock), but I'm happy to initially buy stock based on current demand.

Are you okay with using an app such as Dianxiaomi to ful?ll my orders & add tracking numbers?

My last agent's communication is very slow, so that's why I'm in touch with you right now. I need a reliable person to ship products for all my next stores in the coming years.

If you're interested, please let me know your best total price (unit + shipping) to deliver my product to customers located in the following countries: United States, Canada, Australia, United Kingdom, New Zealand, <any other country you want to ship to>. I'd like to use YunExpress.

I look forward to hearing back from you.




Here's the logic behind all this:

You want to exaggerate your stats a bit if you're a beginner. Tell them you've done this for 1-2 more years than you have, add an extra few 10s of thousands to your monthly ?gures.

You still inform them that the store they'll start working on is new, so it's they'll expect you not to have many orders - but it's okay, because this is just a start.

Then you let them know your last agent's communication was bad. That's just so they don't think you're a complete beginner, and also to know you're expecting higher standards.

End it by asking for prices to buy & ship your product to the main countries you plan to sell to. Don't ask about too many countries and overwhelm them. USA is generally enough for you to get a good idea of whether you're happy with the cost. Aim to ship with YunExpress (about 7-12 business days to USA).








If you have any questions, write them in our discord server.



written by  @eduardbeschea | mavenport



Important!

This document & the information on it are not to be shared by anyone other than Eddie & his admins. Giving away any of our assets (such as this document) will result in a permanent ban from the discord community.
Thank you for understanding.
