






product testing | facebook ads



While testing a new product, the goal within the ?rst few days is not necessarily to be pro?table (although that's always an amazing sign), but to ?nd a few pro?table audiences.

Note: Make sure to only follow this complete strategy if you're using warmed up assets (Facebook pro?les, business managers, ad accounts, pages). If not, then ?rst follow the warm-up process from the safety PDF inside the #knowledge channel.

Anyway, here's the full testing strategy:

Step 1

We'll set up an ABO (ad set budget optimization) campaign with 7-10 ad sets (optimizing for conversion - purchase) @ $20/day, each of them including 4-7 di?erent ad creatives.

Each ad set is targeting a di?erent audience (single interests with an audience above 1M, or stacks of smaller interests amounting up to 1M+ in audience size).

We'll only target the United States.


Step 2

Inside the same ABO campaign, we'll duplicate the 7-10 ad sets we just created, but we'll switch the budget to $10/day. The interests stay exactly the same, but this time we're targeting the top 4 countries (United States, Canada, Australia, United Kingdom).

Schedule everything to start at midnight (ad account time zone).


Use the placements I recommended in our #quick-tips channel, and leave the attribution setting to default (7 days after clicking or 1 day after viewing).

Note: Make sure to always use post IDs (follow the video I posted in the knowledge channel) & UTMs.


If you have a low budget, you have 2 options:

a) Only run step 1 (7-10 ad sets targeting just the United States) - recommended. Check your competitor's ad library and see if they're also running ads just to the United States; if so, then it's easier to pick this option.
b) Run all ad sets @ $10/day (including the ones from step 1).

Anyway, here's what the setup should look like on day 0:




















On day 1, you check your metrics after spending $25-30. If your overall cost per click is massive (above $2-3), then stop the ad sets. Your ads suck.

If your overall cost per click is under $2, then check your ads again after $40-50 in ad spend. If you're getting no sales or not even a reasonable amount of checkouts (~$25 cost per checkout), most likely your ads suck, website sucks, your product sucks - or a combination of them all. In that case, it's up to you whether you want to improve on those variables or cut the product and move on. I personally cut the product and move on because I know I've built everything e?ciently.

Note: Ad sets targeting USA will automatically be more expensive (the cost per click may range between $1-$2). It's not a bad thing. USA is a higher quality audience to target. I would only turn ad sets o? early if the cost per click was around $3 in USA after spending $10+ (per ad set).

For me, most of the time it looks like this (green is pro?table, red is not):
















For day 2, you duplicate all pro?table ad sets (but don't touch the country targeting), and use the suggested interest tool to ?nd similar audiences to target. Don't overthink this, just ?nd mildly related interests and go with them. Schedule them to start at midnight. Cut the unpro?table interests for the day.

Refer to our PDFs about when to turn ad sets/CBOs o? to get a more in-depth view on that topic.

From this point on, the process is somewhat repetitive. After you spend a $1000-2000 on ads for this product, check to see which countries are working best, and then target them inside your next ad sets.

Refer to our scaling PDF after you found a winning product & you're ready to take things to the next level.





If you have any questions, write them in our discord server.



written by  @eduardbeschea | mavenport



Important!

This document & the information on it are not to be shared by anyone other than Eddie & his admins. Giving away any of our assets (such as this document) will result in a permanent ban from the discord community.
Thank you for understanding.
