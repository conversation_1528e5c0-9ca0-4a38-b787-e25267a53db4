disputes & chargebacks

Disputes & chargebacks are a pain in the ass and can ruin your entire business, especially now that
<PERSON><PERSON> charges you a $15 penalty fee whether you win or lose the case. On top of that, payment
processors are much more likely to ban you if you receive a lot of complaints. For example, <PERSON>e will
almost always shut your account down if it’s new and has a chargeback rate of more than 2-3%.
In this PDF you’ll learn the best way to manage the ugly side of eCommerce.
Disclaimer: Please be aware that banks side with their customers more often than not, and while I will
still show you how to increase your chance to win disputes, I will not be responsible for the ones you
lose (even if you’re in the right). That’s just the game.

prevention
First, let’s look at how we can reduce the amount of cases we receive, starting from the top of the
funnel.

1. Transparency
The best way to prevent a large amount of disputes is being honest about your shipping times &
product beneﬁts. When we dropship, we tend to embellish our promises a lot, and that’s ﬁne, but try to
balance proﬁtability with how much you exaggerate.
If you tell people your product has a beneﬁt that you actually don’t oﬀer, of course they’ll be pissed.
Same with advertising the inaccurate times. Sure, you might get a few more sales, but they’ll be
compensated by chargebacks.
So be clear about your product beneﬁts & shipping times (on the product page & at checkout).

2. Customer support
Prompt & polite communication can avoid a lot of disputes. Make sure your website displays ways for
customers to get in touch with you in multiple places so that it’s easier to reach out to you directly
instead of to the bank or PayPal.
Note: When you've been proﬁtable for a while, start adding "Thank you" cards inside your product
packages where, besides thanking your customer for trusting you, you also give them your customer
support e-mail and encourage them to contact you if they need any help at all. This will reduce the
amount of chargebacks you receive because customers will be a lot more likely to get back to you
instead of their bank or PayPal.
On top of that, opposed to the gray hat returns PDF, being more understanding with customers and
oﬀering them refunds when they ask will often play in your favor (if you’re already getting too many
chargebacks).
You have to ﬁgure out if you’re losing more money by refunding orders or by losing chargebacks,
and go with the most beneﬁcial option overall.
Note: If a customer threatens you with contacting their bank to get their money back, just refund them
before they do it unless you’re 100% certain you can win their case. Otherwise you’ll lose the money
they paid you and a $15 penalty fee on top of that (if the payment was made through Stripe).

how to handle disputes & chargebacks
Now we’ll learn the best ways to respond to cases coming from the 2 most popular payment
processors. I’ll teach you how to increase your chance to win.
The ﬁrst thing you should do when you get a case, before you even submit any evidence, is contact
your customer via e-mail (or call them) and solve the problem for them. Be polite and tell them
honestly that chargebacks hurt your business, so you'd be happy to just ﬁx their issue or refund them.
They don't often reply but, when they do, there is a good chance you'll be able to convince them to
remove the chargeback in favor of a solution (which is worth it, because too many chargebacks could
lead to your payment processor closing your account).
If the above didn't work out for any reason, you'll have to provide evidence to your payment processors
that you are in the right.
If you don't have enough evidence to submit for your dispute yet (maybe your customer's order isn't yet
delivered, for example), then set yourself a simple reminder to answer it after an estimated time you
think you'll have to wait to get the information you need. Do that for all disputes in that situation.
Let's learn about how to answer disputes:

The customer has to call their bank and complain about your business in order for you to get a
chargeback on Stripe. Here's what's good and bad about this payment processor:
• you generally have about a month to respond, so there’s a lot of time to ﬁnd a solution
• you get charged a $15 penalty fee whether you win or lose
• your customer’s bank takes the ﬁnal decision (not Stripe), and they most often take the side of their
customer, even if you might be in the right

What to do when you get a dispute
The ﬁrst thing you should do when you get a dispute, besides taking note of the dispute reason (which
we'll talk about lower in this PDF), is Review the claim details.

censored

This will show us in detail why our customer ﬁled a chargeback (based on the information they
provided to their bank), and sometimes, what they say could be disproved easily with tracking numbers
or e-mail conversations. Below is an example where my customer claimed I didn't ship their goods. In
this situation, the most important piece of evidence I have is a last mile tracking number which will
prove them wrong immediately.

If you scroll down lower in the claim details, you'll also see the exact complaint your customer sent to
their bank about you. Here's an example from a diﬀerent dispute of mine:

See, in this case, my customer complained that my support team hadn't replied to them, which is never
the case. Most likely, our e-mails went into their junk folder and that's why they couldn't see them.
In this situation, you check the e-mails you received from this customer and take screenshots of your
replies to use as proof for the dispute. You can also send another sneaky e-mail reply, tailored
speciﬁcally for this dispute, where you pretend to inform the customer politely that you haven't
received any replies in the last X days, and you cannot refund them without a return (adapt what you
say based on the dispute reason). This will help prove to their bank that your customer is in the wrong.
Now that you learned how to get extra information about your disputes, I'll teach you how to handle
the most common types of chargebacks on Stripe - based on their reason.

Dispute reasons
We’ll now focus on the most commonly received types of disputes on Stripe, and I’ll tell you the best
ways to submit evidence for them:

a) Product not received (your customer claims that you didn’t deliver the product)
This is pretty simple. If you didn’t deliver the product yet, make sure you do so within the time Stripe
gave you to answer the dispute, and then submit evidence. If you did deliver the product, then we’ll
respond to the dispute with proof. Here are the important places you'll need to ﬁll in:

Why you should win the dispute?
Select The cardholder received the product or service.

Product or service details
Here, I always provide more information than I’m asked. You can copy-paste the following text:

Our customer ordered a Posture corrector with adjustable straps.
The order was safely delivered (within the advertised time window) by USPS on the 27th of April
to the address our customer provided on their order.
Tracking #: 120840285203939558
Our return policy lasts 14 days from the date of delivery (which was a long time ago). This order
is not eligible for a return anymore.
The customer still has the ordered products and never returned them.
We’re a small start-up and we’re not yet proﬁtable, so we cannot aﬀord to deliver products and
then oﬀer refunds without receiving them back; so we’re asking you kindly to cancel this dispute
since we held our end of the deal. Thank you.

Note: Make sure to replace the red text with your data, remove the yellow text if the product is not out
of the return policy time window you set, and always provide last mile tracking numbers (and never
Chinese ones).

Supporting evidence
1. Shipping documentation
Provide a screenshot of the tracking information from the website of the last mile carrier (such as
USPS, UPS, OnTrac, Royal Mail, etc.).
2. Receipt
Provide a screenshot of the order in the back end of your Shopify store, showing it’s fulﬁlled with the
last mile tracking number, and the customer information matching the delivery address (name,
address).
3. Refund policy (if the customer is out of the return time window)
A screenshot of your return policy on the website (make sure the URL & logo of your website are
showing).
4. Customer communication (optional but ideal)
Provide a screenshot of the customer contradicting the purpose of the dispute (from an e-mail, for
example), only if you have such evidence.
Example: She claims the product wasn’t delivered, but then she e-mailed you and told you it’s
delivered but she doesn’t like it.

Additional information
Write the following text (almost the same as above):

The order was safely delivered (within the advertised time window) by USPS on the 27th of April
to the address our customer provided on their order.
Tracking #: 120840285203939558
Our return policy lasts 14 days from the date of delivery (which was a long time ago). This order
is not eligible for a return anymore.
The customer still has the ordered products and never returned them.
We’re a small start-up and we’re not yet proﬁtable, so we cannot aﬀord to deliver products and
then oﬀer refunds without receiving them back; so we’re asking you kindly to cancel this dispute
since we held our end of the deal. Thank you.

Note: Make sure to replace the red text with your data, remove the yellow text if the product is not out
of the return policy time window you set, and always provide last mile tracking numbers (and never
Chinese ones).

Product or service details
Most of this is already ﬁlled in, you just have to add the last mile tracking number, write the name of the
last mile courier, and select the date the product was delivered.

b) Product unacceptable (your customer claims that your product is not the same as advertised)
You’re generally screwed when you get this type of dispute because the customer can very easily just
lie about your product being broken and the bank will almost always side with them. We’ll still give this
a shot & submit evidence (although we don’t have much):

Why you should win the dispute?
Select The product received was as advertised, or (ideally) if the order was delivered long enough ago
to not be inside your refund policy window, select The dispute was made past the return or
cancellation period of your terms.
Product or service details
Here, I always provide more information than I’m asked (very similar to what I’ll give Stripe at the next
step). You’ll write the following text:

Our customer ordered a Posture corrector with adjustable straps.
The order was safely delivered (within the advertised time window) by USPS on the 27th of April
to the address our customer provided on their order.
Tracking #: 120840285203939558
We thoroughly quality-check every unit in our inventory and ensure it works perfectly. We’re
conﬁdent the product we delivered to our customer is in good condition, and we cannot refund
a shipped order that we paid for without seeing clear proof that something went wrong (which
we have never been provided) or without it being returned back to us.
Our return policy lasts 14 days from the date of delivery (which was a long time ago). This order
is not eligible for a return anymore.
The customer still has the ordered products and never returned them.
We’re a small start-up and we’re not yet proﬁtable, so we cannot aﬀord to deliver products and
then oﬀer refunds without receiving them back; so we’re asking you kindly to cancel this dispute
since we held our end of the deal. Thank you.

Note: Make sure to replace the red text with your data, remove the yellow text if the product is not out
of the return policy time window you set, and always provide last mile tracking numbers (and never
Chinese ones).

Supporting evidence
1. Shipping documentation
Provide a screenshot of the tracking information from the website of the last mile carrier (such as
USPS, UPS, OnTrac, Royal Mail, etc.).
2. Receipt
Provide a screenshot of the order in the back end of your Shopify store, showing it’s fulﬁlled with the
last mile tracking number, and the customer information matching the delivery address (name,
address).
3. Refund policy (if the customer is out of the return time window)
A screenshot of your return policy on the website (make sure the URL & logo of your website are
showing).
4. Customer communication (optional but ideal)
Provide a screenshot of the customer contradicting the purpose of the dispute (from an e-mail, for
example), only if you have such evidence.

Additional information
Write the following text (almost the same as above):

The order was safely delivered (within the advertised time window) by USPS on the 27th of April
to the address our customer provided on their order.
Tracking #: 120840285203939558
We thoroughly quality-check every unit in our inventory and ensure it works perfectly. We’re
conﬁdent the product we delivered to our customer is in good condition, and we cannot refund
a shipped order that we paid for without seeing clear proof that something went wrong (which
we have never been provided) or without it being returned back to us.
Our return policy lasts 14 days from the date of delivery (which was a long time ago). This order
is not eligible for a return anymore.
The customer still has the ordered products and never returned them.
We’re a small start-up and we’re not yet proﬁtable, so we cannot aﬀord to deliver products and
then oﬀer refunds without receiving them back; so we’re asking you kindly to cancel this dispute
since we held our end of the deal. Thank you.

Note: Make sure to replace the red text with your data, remove the yellow text if the product is not out
of the return policy time window you set, and always provide last mile tracking numbers (and never
Chinese ones).

Product or service details
Most of this is ﬁlled in, you just have to add the last mile tracking number, write the name of the last
mile courier, and select the date the product was delivered.

c) Fraudulent (your customer denies authorizing or participating in this transaction)
This is also pretty tough to win because customers - again - can just lie about this but we’ll still give this
a shot & submit evidence:

Why you should win the dispute?
Select The purchase was made by the rightful owner.

Product or service details
Here, I always provide more information than I’m asked (very similar to what I’ll give Stripe at the next
step). You’ll write the following text:

Our customer ordered a Posture corrector with adjustable straps.
The rightful card owner made this purchase from the same IP as the shipping address and has
passed all security veriﬁcations (CVC, Street check, Zip check, etc.) on both Stripe & Shopify screenshot attached. This purchase was clearly authorized by & delivered to the card owner.
The order was safely delivered (within the advertised time window) by USPS on the 27th of April
to the address our customer provided on their order.
Tracking #: 120840285203939558
We thoroughly quality-check every unit in our inventory and ensure it works perfectly. We’re
conﬁdent the product we delivered to our customer is in good condition, and we cannot refund
a shipped order that we paid for without seeing clear proof that something went wrong (which
we have never been provided).
Our return policy lasts 14 days from the date of delivery (which was a long time ago). This order
is not eligible for a return anymore.
The customer still has the ordered products and never returned them.
We’re a small start-up and we’re not yet proﬁtable, so we cannot aﬀord to deliver products and
then oﬀer refunds without receiving them back; so we’re asking you kindly to cancel this dispute
since we held our end of the deal. Thank you.

Note: Make sure to replace the red text with your data, remove the yellow text if the product is not out
of the return policy time window you set, and always provide last mile tracking numbers (and never
Chinese ones).

Supporting evidence
1. Shipping documentation
Provide a screenshot of the tracking information on the website of the last mile carrier (such as USPS,
UPS, OnTrac, Royal Mail, etc.).
2. Receipt
Provide a screenshot of the order in the back end of your Shopify store, showing it’s fulﬁlled with the
last mile tracking number, and the customer information matching the delivery address (name,
address).
3. Other
Provide a screenshot of the Payment method details (which can be found lower on the dispute page)
which shows that the customer passed all security checks. This is what it should look like (I censored my
customer’s data).

censored

censored
censored

censored

censored

4. Customer communication (optional but ideal)
Provide a screenshot of the customer contradicting the purpose of the dispute (from an e-mail, for
example), only if you have such evidence.

Additional information
Write the following text (almost the same as above):

The rightful card owner made this purchase from the same IP as the shipping address and has
passed all security veriﬁcations (CVC, Street check, Zip check, etc.) on both Stripe & Shopify screenshot attached. This purchase was clearly authorized by & delivered to the card owner.
The order was safely delivered (within the advertised time window) by USPS on the 27th of April
to the address our customer provided on their order.
Tracking #: 120840285203939558
We thoroughly quality-check every unit in our inventory and ensure it works perfectly. We’re
conﬁdent the product we delivered to our customer is in good condition, and we cannot refund
a shipped order that we paid for without seeing clear proof that something went wrong (which
we have never been provided).
Our return policy lasts 14 days from the date of delivery (which was a long time ago). This order
is not eligible for a return anymore.
The customer still has the ordered products and never returned them.
We’re a small start-up and we’re not yet proﬁtable, so we cannot aﬀord to deliver products and
then oﬀer refunds without receiving them back; so we’re asking you kindly to cancel this dispute
since we held our end of the deal. Thank you.

Note: Make sure to replace the red text with your data, remove the yellow text if the product is not out
of the return policy time window you set, and always provide last mile tracking numbers (and never
Chinese ones).

Product or service details
Most of this is ﬁlled in, you just have to add the last mile tracking number, write the name of the last
mile courier, and select the date the product was delivered.

When you get a case on PayPal, generally the customer opens it straight on the platform and doesn’t
have to contact their bank. Here's what's good and bad about this payment processor:
• PayPal decides who wins the dispute (or ﬁghts on your behalf with the customer's bank), and most of
the time they’ll take your side
• signiﬁcantly lower dispute fees than Stripe (unless your dispute rate is higher than 1.5% for more than
90 days - this information may be updated at any time)
• you generally have about 15 days to respond, so there’s a lot of time to ﬁnd a solution
• customers don’t always escalate cases, which means sometimes they just close on their own (more
about that lower in the PDF)

How cases work on PayPal
First, it’s important to understand that when a customer opens a case, you’ll be able to respond
straight to them in a case chat (unless they escalate the case right away) - which is a massive beneﬁt
compared to Stripe. That means that you can potentially ﬁx whatever the issue is before they escalate
the case to PayPal (which is when PayPal has to pick a winner).
So how can we manipulate that? 😈
When customers ﬁle a case and don’t escalate it, reply to it very politely & act as if you don’t have
access to their order or you can’t take decisions, and tell them that they’ll be contacted soon by your
customer support team by e-mail. Make them feel like that’s where they need to focus, and not on this
case. Here’s a reply example:

Hello, Annoying Customer.
I’m really sorry to hear that your order was not exactly as you expected. I’ll immediately contact
our customer support team and they’ll reach out to you via e-mail very soon.
They have access to your order information and can help you with a solution!
We appreciate your time & understanding. Have a lovely day!

Once you send the reply, make sure to e-mail the customer (or tell your support rep to) at least 1 time
and ﬁnd a solution (you can use the gray hat returns PDF but give them a better oﬀer so that they can
accept it and then forget about PayPal).
Why do we do this? Because if we defocus the customer from the PayPal case, they often forget to
interact with it, and it will be close due to inactivity.
If the customer keeps replying to the PayPal case, you also continue replying to them while always
providing less information than you do via e-mail, and keep directing them to focus on the e-mail
conversation. Make sure you reply to them via e-mail a lot more promptly than you do on PayPal.

Types of cases
We’ll now only talk about cases that were escalated (because I showed you above how to deal with
cases that weren’t yet escalated). We’ll focus on the most commonly received types of cases on
PayPal, and I’ll tell you the best ways to submit evidence for them:

a) Not received (your customer claims that you didn’t deliver the product)
This is pretty simple. If you didn’t deliver the product yet, make sure you do so within the time PayPal
gave you to answer the case, and then submit evidence. If you did deliver the product, then we’ll
respond to the case now with proof.
Click "Respond” & select Provide fulﬁllment info. Add the last mile tracking number & select the last
mile carrier (if they’re not on the list, select “Other", and then write the carrier name).
Below, add the following screenshots:
1. A screenshot of the tracking information on the website of the last mile carrier (such as USPS, UPS,
OnTrac, Royal Mail, etc.).
2. A screenshot of the order in the back end of your Shopify store, showing it’s fulﬁlled with the last mile
tracking number, and the customer information matching the delivery address (name, address).
3. A screenshot of any communication you had with the customer that can prove that you delivered
the product (optional but ideal) but try to avoid showing they were dissatisﬁed with it, if possible.
Important note: Some customers might try to scam you and claim they received an empty box. In that
case, provide a screenshot of the shipment information (you can get this from your sourcing agent)
that shows the last mile tracking number & the shipment weight. Then, in the box below the place
where you uploaded the screenshots, explain that you also provided proof of the weight of the
package which clearly shows that it weighed a lot more than an empty box.

In the details box below, write the following message:

Our customer ordered a Posture corrector with adjustable straps.
The order was safely delivered (within the advertised time window) by USPS on the 27th of April
to the address our customer provided on their order.
Tracking #: 120840285203939558
Our return policy lasts 14 days from the date of delivery (which was a long time ago). This order
is not eligible for a return anymore.
The customer still has the ordered products and never returned them.
We’re a small start-up and we’re not yet proﬁtable, so we cannot aﬀord to deliver products and
then oﬀer refunds without receiving them back; so we’re asking you kindly to cancel this dispute
since we held our end of the deal. Thank you.

Note: Make sure to replace the red text with your data, remove the yellow text if the product is not out
of the return policy time window you set, and always provide last mile tracking numbers (and never
Chinese ones).

b) Item not as described or other (your customer claims that your product is not the same as
advertised)
Opposed to Stripe (where you’ll almost always lose this type of dispute), PayPal will actually side with
you sometimes.

We’ll respond to this dispute quite similarly to how we do on Stripe.
Click "Respond” & select Provide fulﬁllment info. Add the last mile tracking number & select the last
mile carrier (if they’re not on the list, select “Other", and then write the carrier name).
Below, add the following screenshots:
1. A screenshot of the tracking information on the website of the last mile carrier (such as USPS, UPS,
OnTrac, Royal Mail, etc.).
2. A screenshot of the order in the back end of your Shopify store, showing it’s fulﬁlled with the last mile
tracking number, and the customer information matching the delivery address (name, address).
3. A screenshot of any communication you had with the customer that could prove that the item they
received is not defective (optional but ideal).

In the details box below, write the following message:

Our customer ordered a Posture corrector with adjustable straps.
The order was safely delivered (within the advertised time window) by USPS on the 27th of April
to the address our customer provided on their order.
Tracking #: 120840285203939558
We thoroughly quality-check every unit in our inventory and ensure it works perfectly. We’re
conﬁdent the product we delivered to our customer is in good condition, and we cannot refund
a shipped order that we paid for without seeing clear proof that something went wrong (which
we have never been provided) or without it being returned back to us.
Our return policy lasts 14 days from the date of delivery (which was a long time ago). This order
is not eligible for a return anymore.
The customer still has the ordered products and never returned them.
We’re a small start-up and we’re not yet proﬁtable, so we cannot aﬀord to deliver products and
then oﬀer refunds without receiving them back; so we’re asking you kindly to cancel this dispute
since we held our end of the deal. Thank you.

Note: Make sure to replace the red text with your data, remove the yellow text if the product is not out
of the return policy time window you set, and always provide last mile tracking numbers (and never
Chinese ones).

c) Fraudulent (your customer denies authorizing or participating in this transaction)
This is a little tricky because the customer can just lie to their bank (if the chargeback was made
through it) but PayPal will always try to help us, so we'll respond to the case as explained below:
Click "Respond” & select Provide fulﬁllment info. Add the last mile tracking number & select the last
mile carrier (if they’re not on the list, select “Other", and then write the carrier name).
Below, add the following screenshots:
1. A screenshot of the tracking information on the website of the last mile carrier (such as USPS, UPS,
OnTrac, Royal Mail, etc.).
2. A screenshot of the order in the back end of your Shopify store, showing it’s fulﬁlled with the last mile
tracking number, and the customer information matching the delivery address (name, address).
3. A screenshot of any communication you had with the customer that can prove that they're the
rightful owner (optional but ideal) but try to avoid showing they were dissatisﬁed with it, if possible.
4. In the case details, click on the Transaction ID. Take a screenshot of the entire page of transaction
details, including the Ship To Address section, where you can see that PayPal has conﬁrmed your
customer's address as belonging to the account holder. If it's not conﬁrmed, don't upload this
screenshot (but still use the same message I wrote below & take out the "screenshot uploaded" part).

In the details box below, write the following message:

Our customer ordered a Posture corrector with adjustable straps.
The rightful card owner made this purchase from the same IP as the shipping address and has
passed all security veriﬁcations. The address on the order is already conﬁrmed (screenshot
uploaded) on PayPal as belonging to the PayPal account holder. This purchase was clearly
authorized by & delivered to the card owner.
The order was safely delivered (within the advertised time window) by USPS on the 27th of April
to the address our customer provided on their order.
Tracking #: 120840285203939558
We thoroughly quality-check every unit in our inventory and ensure it works perfectly. We’re
conﬁdent the product we delivered to our customer is in good condition, and we cannot refund
a shipped order that we paid for without seeing clear proof that something went wrong (which
we have never been provided).
Our return policy lasts 14 days from the date of delivery (which was a long time ago). This order
is not eligible for a return anymore.
The customer still has the ordered products and never returned them.
We’re a small start-up and we’re not yet proﬁtable, so we cannot aﬀord to deliver products and
then oﬀer refunds without receiving them back; so we’re asking you kindly to cancel this dispute
since we held our end of the deal. Thank you.

Note: Make sure to replace the red text with your data, remove the yellow text if the product is not out
of the return policy time window you set, and always provide last mile tracking numbers (and never
Chinese ones).

delegating
Once you have mastered handling disputes, you can pass the job on to your customer support agent
or assistant (if you trust them). You can add them to your payment processor accounts with limited
permissions (so they can’t steal your money).

If you have any questions, write them in the 💬︱hustler-chat.

written by

@eduardbeschea | mavenport

Important!
This document & the information on it are not to be shared by anyone other than Eddie & his admins. Giving away any
of our assets (such as this document) will result in a permanent ban from the discord community.
Thank you for understanding.

